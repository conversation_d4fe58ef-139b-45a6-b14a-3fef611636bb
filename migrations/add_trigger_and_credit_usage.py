#!/usr/bin/env python3
"""
Database migration to add:
1. trigger column to chatbots table
2. chatbot_credit_usage table for billing flow
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import get_database_url

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration():
    """Run the migration to add trigger column and credit usage table"""
    
    # Get database URL
    database_url = get_database_url()
    
    # Create engine
    engine = create_engine(database_url)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        logger.info("Starting migration: add_trigger_and_credit_usage")
        
        # 1. Add trigger column to chatbots table
        logger.info("Adding trigger column to chatbots table...")
        session.execute(text("""
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS trigger VARCHAR;
        """))
        
        # 2. Create chatbot_credit_usage table
        logger.info("Creating chatbot_credit_usage table...")
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS chatbot_credit_usage (
                id VARCHAR PRIMARY KEY,
                chatbot_id VARCHAR NOT NULL,
                conversation_id VARCHAR NOT NULL,
                tenant_id VARCHAR NOT NULL,
                question VARCHAR NOT NULL,
                answer VARCHAR NOT NULL,
                credits_used INTEGER DEFAULT 0,
                has_knowledgebase BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                -- Foreign key constraints
                CONSTRAINT fk_credit_usage_chatbot 
                    FOREIGN KEY (chatbot_id) REFERENCES chatbots(id) ON DELETE CASCADE,
                CONSTRAINT fk_credit_usage_conversation 
                    FOREIGN KEY (conversation_id) REFERENCES chatbot_conversations(id) ON DELETE CASCADE
            );
        """))
        
        # 3. Create indexes for better performance
        logger.info("Creating indexes for chatbot_credit_usage table...")
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_chatbot_id 
            ON chatbot_credit_usage(chatbot_id);
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_conversation_id 
            ON chatbot_credit_usage(conversation_id);
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_tenant_id 
            ON chatbot_credit_usage(tenant_id);
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_credit_usage_timestamp 
            ON chatbot_credit_usage(timestamp);
        """))
        
        # Commit the changes
        session.commit()
        logger.info("Migration completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        session.rollback()
        return False
        
    finally:
        session.close()

def verify_migration():
    """Verify that the migration was applied correctly"""
    
    database_url = get_database_url()
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        logger.info("Verifying migration...")
        
        # Check if trigger column exists in chatbots table
        result = session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chatbots' AND column_name = 'trigger';
        """))
        
        if result.fetchone():
            logger.info("✅ trigger column exists in chatbots table")
        else:
            logger.error("❌ trigger column missing in chatbots table")
            return False
        
        # Check if chatbot_credit_usage table exists
        result = session.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_name = 'chatbot_credit_usage';
        """))
        
        if result.fetchone():
            logger.info("✅ chatbot_credit_usage table exists")
        else:
            logger.error("❌ chatbot_credit_usage table missing")
            return False
        
        # Check if indexes exist
        indexes = [
            'idx_credit_usage_chatbot_id',
            'idx_credit_usage_conversation_id',
            'idx_credit_usage_tenant_id',
            'idx_credit_usage_timestamp'
        ]
        
        for index_name in indexes:
            result = session.execute(text("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE indexname = :index_name;
            """), {"index_name": index_name})
            
            if result.fetchone():
                logger.info(f"✅ Index {index_name} exists")
            else:
                logger.warning(f"⚠️  Index {index_name} missing")
        
        logger.info("Migration verification completed!")
        return True
        
    except Exception as e:
        logger.error(f"Migration verification failed: {str(e)}")
        return False
        
    finally:
        session.close()

def rollback_migration():
    """Rollback the migration if needed"""
    
    database_url = get_database_url()
    engine = create_engine(database_url)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        logger.info("Rolling back migration...")
        
        # Drop chatbot_credit_usage table
        session.execute(text("DROP TABLE IF EXISTS chatbot_credit_usage CASCADE;"))
        
        # Remove trigger column from chatbots table
        session.execute(text("ALTER TABLE chatbots DROP COLUMN IF EXISTS trigger;"))
        
        session.commit()
        logger.info("Migration rollback completed!")
        return True
        
    except Exception as e:
        logger.error(f"Migration rollback failed: {str(e)}")
        session.rollback()
        return False
        
    finally:
        session.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Chatbot trigger and credit usage migration")
    parser.add_argument("--rollback", action="store_true", help="Rollback the migration")
    parser.add_argument("--verify", action="store_true", help="Verify the migration")
    
    args = parser.parse_args()
    
    if args.rollback:
        success = rollback_migration()
    elif args.verify:
        success = verify_migration()
    else:
        success = run_migration()
        if success:
            verify_migration()
    
    if success:
        logger.info("✅ Operation completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Operation failed!")
        sys.exit(1)
