#!/usr/bin/env python3
"""
Comprehensive test suite for all chatbot changes implemented:
1. Trigger parameter in chatbot creation
2. Documents.py removal
3. Modified conversation API parameters
4. Enhanced welcome message with OpenAI
5. No knowledgebase scenario handling
6. Billing flow implementation
7. Removed unwanted REST APIs
"""

import pytest
import requests
import json
import os
from datetime import datetime, date
from typing import Dict, Any

# Configuration
BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
TEST_TENANT_ID = "test_tenant_123"
TEST_USER_ID = "test_user_456"

# Mock JWT token for testing
TEST_TOKEN = "Bearer test_token_123"

class TestChatbotTriggerParameter:
    """Test trigger parameter in chatbot creation"""
    
    def test_create_chatbot_with_trigger_new_entity(self):
        """Test creating chatbot with NEW_ENTITY trigger"""
        chatbot_data = {
            "name": "Test Trigger Bot",
            "type": "AI",
            "description": "Test chatbot with trigger",
            "welcomeMessage": "Welcome to our service!",
            "thankYouMessage": "Thank you for your time!",
            "trigger": "NEW_ENTITY",
            "connectedAccount": {
                "displayName": "Test Account",
                "entityType": "LEAD",
                "accountId": 123
            }
        }
        
        response = requests.post(
            f"{BASE_URL}/chatbot/",
            json=chatbot_data,
            headers={"Authorization": TEST_TOKEN}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["trigger"] == "NEW_ENTITY"
        assert data["name"] == "Test Trigger Bot"
        return data["id"]
    
    def test_create_chatbot_with_trigger_existing_entity(self):
        """Test creating chatbot with EXISTING_ENTITY trigger"""
        chatbot_data = {
            "name": "Test Existing Entity Bot",
            "type": "AI",
            "trigger": "EXISTING_ENTITY",
            "connectedAccount": {
                "displayName": "Existing Account",
                "entityType": "CUSTOMER",
                "accountId": 456
            }
        }
        
        response = requests.post(
            f"{BASE_URL}/chatbot/",
            json=chatbot_data,
            headers={"Authorization": TEST_TOKEN}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["trigger"] == "EXISTING_ENTITY"
        return data["id"]
    
    def test_create_chatbot_with_invalid_trigger(self):
        """Test creating chatbot with invalid trigger value"""
        chatbot_data = {
            "name": "Invalid Trigger Bot",
            "type": "AI",
            "trigger": "INVALID_TRIGGER"
        }
        
        response = requests.post(
            f"{BASE_URL}/chatbot/",
            json=chatbot_data,
            headers={"Authorization": TEST_TOKEN}
        )
        
        assert response.status_code == 400
        assert "Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'" in response.text

class TestDocumentsEndpointRemoval:
    """Test that documents endpoints are removed"""
    
    def test_documents_endpoints_not_exist(self):
        """Test that documents endpoints return 404"""
        endpoints = [
            "/documents",
            "/documents/test_id"
        ]
        
        for endpoint in endpoints:
            response = requests.get(
                f"{BASE_URL}{endpoint}",
                headers={"Authorization": TEST_TOKEN}
            )
            assert response.status_code == 404

class TestModifiedConversationAPI:
    """Test modified conversation API parameters"""
    
    def test_start_conversation_with_new_parameters(self):
        """Test starting conversation with entityType, connectedAccountId, trigger"""
        # First create a chatbot with the required parameters
        chatbot_data = {
            "name": "Conversation Test Bot",
            "type": "AI",
            "trigger": "NEW_ENTITY",
            "connectedAccount": {
                "displayName": "Test Account",
                "entityType": "LEAD",
                "accountId": 789
            }
        }
        
        create_response = requests.post(
            f"{BASE_URL}/chatbot/",
            json=chatbot_data,
            headers={"Authorization": TEST_TOKEN}
        )
        assert create_response.status_code == 200
        
        # Now test the conversation API
        conversation_data = {
            "message": "Hello, I need help",
            "entityType": "LEAD",
            "connectedAccountId": 789,
            "trigger": "NEW_ENTITY"
        }
        
        response = requests.post(
            f"{BASE_URL}/chatbot/conversations",
            json=conversation_data,
            headers={"Authorization": TEST_TOKEN}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "conversation_id" in data
        assert "answer" in data
        assert "nextQuestion" in data
        return data["conversation_id"]
    
    def test_start_conversation_with_invalid_trigger(self):
        """Test starting conversation with invalid trigger"""
        conversation_data = {
            "message": "Hello",
            "entityType": "LEAD",
            "connectedAccountId": 123,
            "trigger": "INVALID_TRIGGER"
        }
        
        response = requests.post(
            f"{BASE_URL}/chatbot/conversations",
            json=conversation_data,
            headers={"Authorization": TEST_TOKEN}
        )
        
        assert response.status_code == 400
        assert "Trigger must be either 'NEW_ENTITY' or 'EXISTING_ENTITY'" in response.text

class TestBillingFlow:
    """Test billing flow implementation"""
    
    def test_credit_usage_tracking(self):
        """Test that credit usage is tracked properly"""
        # This would require a full conversation flow
        # For now, test the credit usage endpoints
        
        # Test credit usage endpoint
        response = requests.get(
            f"{BASE_URL}/chatbot/test_chatbot_id/credit-usage",
            headers={"Authorization": TEST_TOKEN}
        )
        
        # Should return 404 for non-existent chatbot or empty list
        assert response.status_code in [200, 404]
    
    def test_credit_summary_endpoint(self):
        """Test credit summary endpoint"""
        response = requests.get(
            f"{BASE_URL}/chatbot/test_chatbot_id/credit-summary",
            headers={"Authorization": TEST_TOKEN}
        )
        
        # Should return 404 for non-existent chatbot
        assert response.status_code in [200, 404]

class TestRemovedRabbitMQEndpoints:
    """Test that RabbitMQ admin endpoints are removed"""
    
    def test_rabbitmq_endpoints_removed(self):
        """Test that RabbitMQ admin endpoints return 404"""
        endpoints = [
            "/admin/rabbitmq/restart",
            "/admin/rabbitmq/recover",
            "/health/rabbitmq"
        ]
        
        for endpoint in endpoints:
            response = requests.post(f"{BASE_URL}{endpoint}")
            assert response.status_code == 404
            
            response = requests.get(f"{BASE_URL}{endpoint}")
            assert response.status_code == 404

def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🧪 Running Comprehensive Chatbot Changes Tests")
    print("=" * 60)
    
    test_classes = [
        TestChatbotTriggerParameter,
        TestDocumentsEndpointRemoval,
        TestModifiedConversationAPI,
        TestBillingFlow,
        TestRemovedRabbitMQEndpoints
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = []
    
    for test_class in test_classes:
        print(f"\n📋 Testing {test_class.__name__}")
        print("-" * 40)
        
        instance = test_class()
        methods = [method for method in dir(instance) if method.startswith('test_')]
        
        for method_name in methods:
            total_tests += 1
            try:
                method = getattr(instance, method_name)
                method()
                print(f"  ✅ {method_name}")
                passed_tests += 1
            except Exception as e:
                print(f"  ❌ {method_name}: {str(e)}")
                failed_tests.append(f"{test_class.__name__}.{method_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} passed")
    
    if failed_tests:
        print(f"\n❌ Failed Tests:")
        for failure in failed_tests:
            print(f"  - {failure}")
    else:
        print("🎉 All tests passed!")
    
    return len(failed_tests) == 0

if __name__ == "__main__":
    success = run_comprehensive_tests()
    exit(0 if success else 1)
